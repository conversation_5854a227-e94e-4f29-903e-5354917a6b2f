import { NextRequest, NextResponse } from 'next/server';

// Import the redshift connection
import { getRedshiftPool } from '@/lib/api/redshift';
import { getToken } from 'next-auth/jwt';

// --- Types ---

// Query Parameter Types
type ValidGroupByTime = 'day' | 'week' | 'month' | 'quarter' | 'year';
type ValidGroupByDimension = 'brand' | 'brandGroup' | 'marketplace';
type ValidCurrency = 'CAD' | 'USD';

type AmazonKpiQueryParams = {
  startDate?: string; // YYYY-MM-DD
  endDate?: string;   // YYYY-MM-DD
  currency?: ValidCurrency;
  brands?: string | string[];
  brandGroups?: string | string[];
  kpis?: string | string[];
  groupByTime?: ValidGroupByTime;
  groupByDimension?: ValidGroupByDimension;
  marketplaces?: string | string[];
};

// Response Types
type KpiSummary = {
  value: number | null;
};

type TimeSeriesDataPoint = {
  date: string;
  value: number | null;
};

type KpiData = {
  summary: KpiSummary;
  timeSeries: TimeSeriesDataPoint[];
};

type SimpleKpiResponse = {
  [kpiName: string]: KpiData;
};

type GroupedKpiResponse = {
  [kpiName: string]: {
    [dimensionValue: string]: KpiData;
  };
};

type AmazonKpiResponse = SimpleKpiResponse | GroupedKpiResponse;

type ErrorResponse = {
  error: string;
  details?: string;
};

// --- Constants ---
const ALL_AVAILABLE_AMAZON_KPIS: Set<string> = new Set([
  'Net Revenue', 'Branded ACOS', 'Unbranded ACOS', 'Adspend', 'TACOS', 'Excess Units', 'Instock Rate'
]);

// --- Validation Functions ---

function validateParameters(searchParams: URLSearchParams): {
  startDate: string;
  endDate: string;
  currency: ValidCurrency;
  brands: string[];
  brandGroups: string[];
  requestedKpis: string[];
  groupByTime: ValidGroupByTime;
  groupByDimension: ValidGroupByDimension | null;
  marketplaces: string[];
} {
  // Date validation
  const startDateParam = searchParams.get('startDate');
  const endDateParam = searchParams.get('endDate');

  const today = new Date();
  const defaultEndDate = today.toISOString().split('T')[0];
  const defaultStartDate = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

  const validatedStartDate = startDateParam || defaultStartDate;
  const validatedEndDate = endDateParam || defaultEndDate;

  // Currency validation
  const currencyParam = searchParams.get('currency') as ValidCurrency;
  const currency: ValidCurrency = ['CAD', 'USD'].includes(currencyParam) ? currencyParam : 'CAD';

  // Brands validation
  const brandsParam = searchParams.get('brands');
  const brands: string[] = brandsParam ? brandsParam.split(',').map(b => b.trim()).filter(Boolean) : [];

  // Brand Groups validation
  const brandGroupsParam = searchParams.get('brandGroups');
  const brandGroups: string[] = brandGroupsParam ? brandGroupsParam.split(',').map(bg => bg.trim()).filter(Boolean) : [];

  // Marketplaces validation
  const marketplacesParam = searchParams.get('marketplaces');
  const marketplaces: string[] = marketplacesParam ? marketplacesParam.split(',').map(m => m.trim()).filter(Boolean) : [];

  // KPIs validation
  const kpisParam = searchParams.get('kpis');
  let requestedKpis: string[] = [];
  if (kpisParam) {
    const kpiList = kpisParam.split(',').map(k => k.trim()).filter(Boolean);
    requestedKpis = kpiList.filter(kpi => ALL_AVAILABLE_AMAZON_KPIS.has(kpi));
  } else {
    requestedKpis = Array.from(ALL_AVAILABLE_AMAZON_KPIS);
  }

  // Group By Time validation
  const groupByTimeParam = searchParams.get('groupByTime') as ValidGroupByTime;
  const groupByTime: ValidGroupByTime = ['day', 'week', 'month', 'quarter', 'year'].includes(groupByTimeParam) ? groupByTimeParam : 'day';

  // Group By Dimension validation
  const groupByDimensionParam = searchParams.get('groupByDimension') as ValidGroupByDimension;
  const groupByDimension: ValidGroupByDimension | null = ['brand', 'brandGroup', 'marketplace'].includes(groupByDimensionParam) ? groupByDimensionParam : null;

  return {
    startDate: validatedStartDate,
    endDate: validatedEndDate,
    currency,
    brands,
    brandGroups,
    requestedKpis,
    groupByTime,
    groupByDimension,
    marketplaces
  };
}

// --- SQL Query Builder ---
async function buildAmazonKpiQuery(
  params: ReturnType<typeof validateParameters>,
  kpi: string,
  tokenInfo: { isImpersonating?: boolean, isSuperAdmin?: boolean, tokenBrands?: number[] }
): Promise<{ query: string; queryParams: (string | number | number[])[] }> {
  let sql = "";
  const queryParams: (string | number | number[])[] = [];
  let paramIndex = 1;
  const conditions: string[] = [];

  // Base conditions
  conditions.push(`k.purchase_date >= $${paramIndex++}`);
  queryParams.push(params.startDate);
  conditions.push(`k.purchase_date <= $${paramIndex++}`);
  queryParams.push(params.endDate);

  // Brand filtering
  if (params.brands.length > 0) {
    conditions.push(`k.brand = ANY($${paramIndex++})`);
    queryParams.push(params.brands);
  }

  // Currency conversion expression
  const currencyExpression = params.currency === 'USD'
    ? `k.total_net_revenue * NVL(ex.exchange_rate, 1)/NVL(ex2.exchange_rate, 1)`
    : `k.total_net_revenue * NVL(ex.exchange_rate, 1)`;

  // Build query based on KPI type
  switch (kpi) {
    case 'Net Revenue':
      sql = `
        SELECT
          DATE_TRUNC('${params.groupByTime}', k.purchase_date)::DATE::VARCHAR AS date,
          SUM(${currencyExpression}) as value
        FROM dwh_ai.ai_reporting_ds_sales_order_items k
          LEFT JOIN dwh_ai.ai_reporting_brands b ON k.brand = b.name
          LEFT JOIN dwh_ai.ai_reporting_exchange_rates_history ex
                 ON ex.from_currency = k.currency
                AND ex.to_currency = 'CAD'
                AND ex.snapshot_date::DATE = k.purchase_date::DATE
          ${params.currency === 'USD' ? `
          LEFT JOIN dwh_ai.ai_reporting_exchange_rates_history ex2
                 ON ex2.from_currency = 'USD'
                AND ex2.to_currency = 'CAD'
                AND ex2.snapshot_date::DATE = k.purchase_date::DATE` : ''}
        WHERE k.sales_channel_type = 'Amazon'
          AND ${conditions.join(' AND ')}
        GROUP BY DATE_TRUNC('${params.groupByTime}', k.purchase_date)::DATE::VARCHAR
        ORDER BY date
      `;
      break;

    case 'Adspend':
      const adspendCurrencyExpression = params.currency === 'USD'
        ? `k.total_cost * NVL(ex.exchange_rate, 1)/NVL(ex2.exchange_rate, 1)`
        : `k.total_cost * NVL(ex.exchange_rate, 1)`;

      sql = `
        SELECT
          DATE_TRUNC('${params.groupByTime}', k.date)::DATE::VARCHAR AS date,
          SUM(${adspendCurrencyExpression}) as value
        FROM dwh_ai.ai_reporting_ds_marketing_advertisings k
          LEFT JOIN dwh_ai.ai_reporting_brands b ON k.brand = b.name
          LEFT JOIN dwh_ai.ai_reporting_exchange_rates_history ex
                 ON ex.from_currency = k.currency
                AND ex.to_currency = 'CAD'
                AND ex.snapshot_date::DATE = k.date::DATE
          ${params.currency === 'USD' ? `
          LEFT JOIN dwh_ai.ai_reporting_exchange_rates_history ex2
                 ON ex2.from_currency = 'USD'
                AND ex2.to_currency = 'CAD'
                AND ex2.snapshot_date::DATE = k.date::DATE` : ''}
        WHERE k.sales_channel_type = 'Amazon'
          AND k.date >= $1 AND k.date <= $2
          ${params.brands.length > 0 ? 'AND k.brand = ANY($3)' : ''}
        GROUP BY DATE_TRUNC('${params.groupByTime}', k.date)::DATE::VARCHAR
        ORDER BY date
      `;
      break;

    case 'Branded ACOS':
      const brandedAcosCurrencyExpression = params.currency === 'USD'
        ? `k.total_cost * NVL(ex.exchange_rate, 1)/NVL(ex2.exchange_rate, 1)`
        : `k.total_cost * NVL(ex.exchange_rate, 1)`;
      const brandedAcosSalesExpression = params.currency === 'USD'
        ? `k.total_sales * NVL(ex.exchange_rate, 1)/NVL(ex2.exchange_rate, 1)`
        : `k.total_sales * NVL(ex.exchange_rate, 1)`;

      sql = `
        SELECT
          '${params.startDate}' as date,
          CASE
            WHEN SUM(${brandedAcosSalesExpression}) > 0
            THEN SUM(${brandedAcosCurrencyExpression})/SUM(${brandedAcosSalesExpression})
            ELSE NULL
          END as value
        FROM dwh_ai.ai_reporting_ds_marketing_advertisings k
          LEFT JOIN dwh_ai.ai_reporting_brands b ON k.brand = b.name
          LEFT JOIN dwh_ai.ai_reporting_exchange_rates_history ex
                 ON ex.from_currency = k.currency
                AND ex.to_currency = 'CAD'
                AND ex.snapshot_date::DATE = k.date::DATE
          ${params.currency === 'USD' ? `
          LEFT JOIN dwh_ai.ai_reporting_exchange_rates_history ex2
                 ON ex2.from_currency = 'USD'
                AND ex2.to_currency = 'CAD'
                AND ex2.snapshot_date::DATE = k.date::DATE` : ''}
        WHERE k.advertising_platform = 'Amazon Ads'
          AND k.total_cost > 0
          AND UPPER(k.campaign_name) LIKE '%BC%'
          AND k.date >= $1 AND k.date <= $2
          ${params.brands.length > 0 ? 'AND k.brand = ANY($3)' : ''}
      `;
      break;

    case 'Unbranded ACOS':
      const unbrandedAcosCurrencyExpression = params.currency === 'USD'
        ? `k.total_cost * NVL(ex.exchange_rate, 1)/NVL(ex2.exchange_rate, 1)`
        : `k.total_cost * NVL(ex.exchange_rate, 1)`;
      const unbrandedAcosSalesExpression = params.currency === 'USD'
        ? `k.total_sales * NVL(ex.exchange_rate, 1)/NVL(ex2.exchange_rate, 1)`
        : `k.total_sales * NVL(ex.exchange_rate, 1)`;

      sql = `
        SELECT
          '${params.startDate}' as date,
          CASE
            WHEN SUM(${unbrandedAcosSalesExpression}) > 0
            THEN SUM(${unbrandedAcosCurrencyExpression})/SUM(${unbrandedAcosSalesExpression})
            ELSE NULL
          END as value
        FROM dwh_ai.ai_reporting_ds_marketing_advertisings k
          LEFT JOIN dwh_ai.ai_reporting_brands b ON k.brand = b.name
          LEFT JOIN dwh_ai.ai_reporting_exchange_rates_history ex
                 ON ex.from_currency = k.currency
                AND ex.to_currency = 'CAD'
                AND ex.snapshot_date::DATE = k.date::DATE
          ${params.currency === 'USD' ? `
          LEFT JOIN dwh_ai.ai_reporting_exchange_rates_history ex2
                 ON ex2.from_currency = 'USD'
                AND ex2.to_currency = 'CAD'
                AND ex2.snapshot_date::DATE = k.date::DATE` : ''}
        WHERE k.sales_channel_type = 'Amazon'
          AND k.total_cost > 0
          AND UPPER(k.campaign_name) NOT LIKE '%BC%'
          AND k.date >= $1 AND k.date <= $2
          ${params.brands.length > 0 ? 'AND k.brand = ANY($3)' : ''}
      `;
      break;

    default:
      // For other KPIs, return a simple mock query for now
      sql = `SELECT '${params.startDate}' as date, 0 as value`;
      break;
  }

  return { query: sql, queryParams };
}

// --- Main Handler ---
export async function GET(request: NextRequest) {
  console.log('[Amazon KPIs API] Request received');

  const token = await getToken({ req: request, secret: process.env.NEXTAUTH_SECRET });

  if (!token) {
    console.log('[Amazon KPIs API] Unauthorized request - no token');
    return NextResponse.json({ error: 'Unauthorized' } as ErrorResponse, { status: 401 });
  }

  const {
    isImpersonating,
    isSuperAdmin,
    brands: tokenBrandsArrayRaw,
  } = token as { isImpersonating?: boolean; isSuperAdmin?: boolean; brands?: unknown };

  const tokenBrandsArray: number[] | undefined = Array.isArray(tokenBrandsArrayRaw)
    ? tokenBrandsArrayRaw.filter(id => typeof id === 'number')
    : undefined;

  const tokenInfo = {
    isImpersonating,
    isSuperAdmin,
    tokenBrands: tokenBrandsArray
  };

  let client;
  try {
    const { searchParams } = new URL(request.url);
    console.log('[Amazon KPIs API] Query parameters:', Object.fromEntries(searchParams.entries()));

    const validatedParams = validateParameters(searchParams);
    console.log('[Amazon KPIs API] Validated parameters:', validatedParams);

    const redshiftPool = getRedshiftPool();
    client = await redshiftPool.connect();

    const response: AmazonKpiResponse = {};

    // Process each requested KPI
    for (const kpi of validatedParams.requestedKpis) {
      try {
        const { query, queryParams: sqlParams } = await buildAmazonKpiQuery(validatedParams, kpi, tokenInfo);
        console.log(`[Amazon KPIs API] Executing query for ${kpi}:`, query);

        const result = await client.query(query, sqlParams);

        // Process results
        const timeSeries: TimeSeriesDataPoint[] = result.rows.map(row => ({
          date: row.date,
          value: row.value ? parseFloat(row.value) : null
        }));

        const totalValue = timeSeries.reduce((sum, point) =>
          sum + (point.value || 0), 0
        );

        response[kpi] = {
          summary: { value: totalValue },
          timeSeries
        };
      } catch (kpiError) {
        console.error(`[Amazon KPIs API] Error processing KPI ${kpi}:`, kpiError);
        // Continue with other KPIs
        response[kpi] = {
          summary: { value: null },
          timeSeries: []
        };
      }
    }

    console.log('[Amazon KPIs API] Returning response');
    return NextResponse.json(response);

  } catch (error) {
    console.error('[Amazon KPIs API] Error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      } as ErrorResponse,
      { status: 500 }
    );
  } finally {
    if (client) {
      client.release();
    }
  }
}
