KPI,CAD Query,USD Query,Status
Net revenue by merchant & marketplace,"SELECT k.purchase_date::DATE as date,
       b.group AS brand_group,
       k.brand AS brand,
       k.source_system as merchant,
       k.sales_channel_name as marketplace,
       'CAD' AS currency,
       SUM(k.total_net_revenue * NVL(ex.exchange_rate, 1)) as net_revenue
FROM dwh_ai.ai_reporting_ds_sales_order_items k
  LEFT JOIN dwh_ai.ai_reporting_brands b ON k.brand = b.name
  LEFT JOIN dwh_ai.ai_reporting_exchange_rates_history ex
         ON ex.from_currency = k.currency
        AND ex.to_currency = 'CAD'
        AND ex.snapshot_date::DATE = k.purchase_date::DATE
WHERE 1 = 1
AND   k.purchase_date >= '2024-01-01'
AND   k.sales_channel_type = 'Amazon'
GROUP BY k.purchase_date::DATE,
         k.brand,
         b.group,
         k.source_system,
         k.sales_channel_name
;","SELECT k.purchase_date::DATE as date,
       b.group AS brand_group,
       k.brand AS brand,
       k.source_system as merchant,
       k.sales_channel_name as marketplace,
       'USD' AS currency,
       SUM(k.total_net_revenue * NVL(ex.exchange_rate, 1)/NVL(ex2.exchange_rate, 1)) as net_revenue
FROM dwh_ai.ai_reporting_ds_sales_order_items k
  LEFT JOIN dwh_ai.ai_reporting_brands b ON k.brand = b.name
  LEFT JOIN dwh_ai.ai_reporting_exchange_rates_history ex
         ON ex.from_currency = k.currency
        AND ex.to_currency = 'CAD'
        AND ex.snapshot_date::DATE = k.purchase_date::DATE
    LEFT JOIN dwh_ai.ai_reporting_exchange_rates_history ex2
         ON ex2.from_currency = 'USD'
        AND ex2.to_currency = 'CAD'
        AND ex2.snapshot_date::DATE = k.purchase_date::DATE
WHERE 1 = 1
AND   k.purchase_date >= '2024-01-01'
AND   k.sales_channel_type = 'Amazon'
GROUP BY k.purchase_date::DATE,
         k.brand,
         b.group,
         k.source_system,
         k.sales_channel_name
;",OK
Branded ACOS,"SELECT 
       b.group AS brand_group,
       k.brand AS brand,
       'Amazon ' || LEFT(k.advertising_account_name, LENGTH(k.advertising_account_name) - 3) as merchant,
       k.sales_channel_name as marketplace,
       'CAD' AS currency,
       SUM(k.total_cost * NVL(ex.exchange_rate, 1))/SUM(k.total_sales * NVL(ex.exchange_rate, 1)) as branded_acos
FROM dwh_ai.ai_reporting_ds_marketing_advertisings k
  LEFT JOIN dwh_ai.ai_reporting_brands b ON k.brand = b.name
  LEFT JOIN dwh_ai.ai_reporting_exchange_rates_history ex
         ON ex.from_currency = k.currency
        AND ex.to_currency = 'CAD'
        AND ex.snapshot_date::DATE = k.date::DATE
WHERE 1 = 1
AND   k.date >= '2024-01-01'
AND   k.advertising_platform = 'Amazon Ads'
AND   k.total_cost > 0
AND   UPPER(k.campaign_name) LIKE '%BC%'
GROUP BY k.brand,
         b.group,
         merchant,
         marketplace
;","SELECT 
       b.group AS brand_group,
       k.brand AS brand,
       'Amazon ' || LEFT(k.advertising_account_name, LENGTH(k.advertising_account_name) - 3) as merchant,
       k.sales_channel_name as marketplace,
       'USD' AS currency,
       SUM(k.total_cost * NVL(ex.exchange_rate, 1)/NVL(ex2.exchange_rate, 1))/SUM(k.total_sales * NVL(ex.exchange_rate, 1)/NVL(ex2.exchange_rate, 1)) as branded_acos
FROM dwh_ai.ai_reporting_ds_marketing_advertisings k
  LEFT JOIN dwh_ai.ai_reporting_brands b ON k.brand = b.name
  LEFT JOIN dwh_ai.ai_reporting_exchange_rates_history ex
         ON ex.from_currency = k.currency
        AND ex.to_currency = 'CAD'
        AND ex.snapshot_date::DATE = k.date::DATE
  LEFT JOIN dwh_ai.ai_reporting_exchange_rates_history ex2
         ON ex2.from_currency = 'USD'
        AND ex2.to_currency = 'CAD'
        AND ex2.snapshot_date::DATE = k.date::DATE
WHERE 1 = 1
AND   k.date >= '2024-01-01'
AND   k.sales_channel_type = 'Amazon'
AND   k.total_cost > 0
AND   UPPER(k.campaign_name) LIKE '%BC%'
GROUP BY k.brand,
         b.group,
         merchant,
         marketplace
;",OK
Unbranded ACOS,"SELECT 
       b.group AS brand_group,
       k.brand AS brand,
       'Amazon ' || LEFT(k.advertising_account_name, LENGTH(k.advertising_account_name) - 3) as merchant,
       k.sales_channel_name as marketplace,
       'CAD' AS currency,
       SUM(k.total_cost * NVL(ex.exchange_rate, 1))/SUM(k.total_sales * NVL(ex.exchange_rate, 1)) as unbranded_acos
FROM dwh_ai.ai_reporting_ds_marketing_advertisings k
  LEFT JOIN dwh_ai.ai_reporting_brands b ON k.brand = b.name
  LEFT JOIN dwh_ai.ai_reporting_exchange_rates_history ex
         ON ex.from_currency = k.currency
        AND ex.to_currency = 'CAD'
        AND ex.snapshot_date::DATE = k.date::DATE
WHERE 1 = 1
AND   k.date >= '2024-01-01'
AND   k.sales_channel_type = 'Amazon'
AND   k.total_cost > 0
AND   UPPER(k.campaign_name) NOT LIKE '%BC%'
GROUP BY k.brand,
         b.group,
         merchant,
         marketplace
;","SELECT 
       b.group AS brand_group,
       k.brand AS brand,
       'Amazon ' || LEFT(k.advertising_account_name, LENGTH(k.advertising_account_name) - 3) as merchant,
       k.sales_channel_name as marketplace,
       'USD' AS currency,
       SUM(k.total_cost * NVL(ex.exchange_rate, 1)/NVL(ex2.exchange_rate, 1))/SUM(k.total_sales * NVL(ex.exchange_rate, 1)/NVL(ex2.exchange_rate, 1)) as unbranded_acos
FROM dwh_ai.ai_reporting_ds_marketing_advertisings k
  LEFT JOIN dwh_ai.ai_reporting_brands b ON k.brand = b.name
  LEFT JOIN dwh_ai.ai_reporting_exchange_rates_history ex
         ON ex.from_currency = k.currency
        AND ex.to_currency = 'CAD'
        AND ex.snapshot_date::DATE = k.date::DATE
  LEFT JOIN dwh_ai.ai_reporting_exchange_rates_history ex2
         ON ex2.from_currency = 'USD'
        AND ex2.to_currency = 'CAD'
        AND ex2.snapshot_date::DATE = k.date::DATE
WHERE 1 = 1
AND   k.date >= '2024-01-01'
AND   k.sales_channel_type = 'Amazon'
AND   k.total_cost > 0
AND   UPPER(k.campaign_name) NOT LIKE '%BC%'
GROUP BY k.brand,
         b.group,
         merchant,
         marketplace
;",OK
Adspend,"SELECT k.date,
       b.group AS brand_group,
       k.brand AS brand,
       'Amazon ' || LEFT(k.advertising_account_name, LENGTH(k.advertising_account_name) - 3) as merchant,
       k.sales_channel_name as marketplace,
       'CAD' AS currency,
       SUM(k.total_cost * NVL(ex.exchange_rate, 1)) as adspend
FROM dwh_ai.ai_reporting_ds_marketing_advertisings k
  LEFT JOIN dwh_ai.ai_reporting_brands b ON k.brand = b.name
  LEFT JOIN dwh_ai.ai_reporting_exchange_rates_history ex
         ON ex.from_currency = k.currency
        AND ex.to_currency = 'CAD'
        AND ex.snapshot_date::DATE = k.date::DATE
WHERE 1 = 1
AND   k.date >= '2025-01-01'
AND   k.sales_channel_type = 'Amazon'
GROUP BY k.date,
         k.brand,
         b.group,
         merchant,
         marketplace
;","SELECT k.date,
       b.group AS brand_group,
       k.brand AS brand,
       'Amazon ' || LEFT(k.advertising_account_name, LENGTH(k.advertising_account_name) - 3) as merchant,
       k.sales_channel_name as marketplace,
       'USD' AS currency,
       SUM(k.total_cost * NVL(ex.exchange_rate, 1)/NVL(ex2.exchange_rate, 1)) as adspend
FROM dwh_ai.ai_reporting_ds_marketing_advertisings k
  LEFT JOIN dwh_ai.ai_reporting_brands b ON k.brand = b.name
  LEFT JOIN dwh_ai.ai_reporting_exchange_rates_history ex
         ON ex.from_currency = k.currency
        AND ex.to_currency = 'CAD'
        AND ex.snapshot_date::DATE = k.date::DATE
  LEFT JOIN dwh_ai.ai_reporting_exchange_rates_history ex2
         ON ex2.from_currency = 'USD'
        AND ex2.to_currency = 'CAD'
        AND ex2.snapshot_date::DATE = k.date::DATE
WHERE 1 = 1
AND   k.date >= '2025-01-01'
AND   k.sales_channel_type = 'Amazon'
GROUP BY k.date,
         k.brand,
         b.group,
         merchant,
         marketplace",OK
"TACOS by merchant, marketplace","with tmp_net_revenue AS
(
SELECT 
       b.group AS brand_group,
       k.brand AS brand,
       k.source_system as merchant,
       k.sales_channel_name as marketplace,
       'CAD' AS currency,
       SUM(k.total_net_revenue * NVL(ex.exchange_rate, 1)) as net_revenue
FROM dwh_ai.ai_reporting_ds_sales_order_items k
  LEFT JOIN dwh_ai.ai_reporting_brands b ON k.brand = b.name
  LEFT JOIN dwh_ai.ai_reporting_exchange_rates_history ex
         ON ex.from_currency = k.currency
        AND ex.to_currency = 'CAD'
        AND ex.snapshot_date::DATE = k.purchase_date::DATE
WHERE 1 = 1
AND   k.purchase_date >= '2024-01-01'
AND   k.sales_channel_type = 'Amazon'
GROUP BY 
         k.brand,
         b.group,
         k.source_system,
         k.sales_channel_name
),
tmp_adspend AS 
(
SELECT 
       b.group AS brand_group,
       k.brand AS brand,
       'Amazon ' || LEFT(k.advertising_account_name, LENGTH(k.advertising_account_name) - 3) as merchant,
       k.sales_channel_name as marketplace,
       'CAD' AS currency,
       SUM(k.total_cost * NVL(ex.exchange_rate, 1)) as adspend
FROM dwh_ai.ai_reporting_ds_marketing_advertisings k
  LEFT JOIN dwh_ai.ai_reporting_brands b ON k.brand = b.name
  LEFT JOIN dwh_ai.ai_reporting_exchange_rates_history ex
         ON ex.from_currency = k.currency
        AND ex.to_currency = 'CAD'
        AND ex.snapshot_date::DATE = k.date::DATE
WHERE 1 = 1
AND   k.date >= '2025-01-01'
AND   k.sales_channel_type = 'Amazon'
GROUP BY 
         k.brand,
         b.group,
         merchant,
         marketplace
)
SELECT brand,
       brand_group,
       merchant,
       marketplace,
       currency,
       SUM(adspend) / SUM(net_revenue) AS tacos
FROM
(
  SELECT brand, brand_group, merchant, marketplace, currency, net_revenue, 0 as adspend from tmp_net_revenue
  UNION ALL
  SELECT brand, brand_group, merchant, marketplace, currency, 0 net_revenue, adspend from tmp_adspend
) 
GROUP BY brand,
         brand_group,
         merchant,
         marketplace,
         currency
HAVING SUM(net_revenue) > 0


","with tmp_net_revenue AS
(
SELECT k.purchase_date::DATE as date,
       b.group AS brand_group,
       k.brand AS brand,
       k.source_system as merchant,
       k.sales_channel_name as marketplace,
       'USD' AS currency,
       SUM(k.total_net_revenue * NVL(ex.exchange_rate, 1)/NVL(ex2.exchange_rate, 1)) as net_revenue
FROM dwh_ai.ai_reporting_ds_sales_order_items k
  LEFT JOIN dwh_ai.ai_reporting_brands b ON k.brand = b.name
  LEFT JOIN dwh_ai.ai_reporting_exchange_rates_history ex
         ON ex.from_currency = k.currency
        AND ex.to_currency = 'CAD'
        AND ex.snapshot_date::DATE = k.purchase_date::DATE
    LEFT JOIN dwh_ai.ai_reporting_exchange_rates_history ex2
         ON ex2.from_currency = 'USD'
        AND ex2.to_currency = 'CAD'
        AND ex2.snapshot_date::DATE = k.purchase_date::DATE
WHERE 1 = 1
AND   k.purchase_date >= '2024-01-01'
AND   k.sales_channel_type = 'Amazon'
GROUP BY k.purchase_date::DATE,
         k.brand,
         b.group,
         k.source_system,
         k.sales_channel_name
),
tmp_adspend AS 
(
SELECT k.date,
       b.group AS brand_group,
       k.brand AS brand,
       'Amazon ' || LEFT(k.advertising_account_name, LENGTH(k.advertising_account_name) - 3) as merchant,
       k.sales_channel_name as marketplace,
       'USD' AS currency,
       SUM(k.total_cost * NVL(ex.exchange_rate, 1)/NVL(ex2.exchange_rate, 1)) as adspend
FROM dwh_ai.ai_reporting_ds_marketing_advertisings k
  LEFT JOIN dwh_ai.ai_reporting_brands b ON k.brand = b.name
  LEFT JOIN dwh_ai.ai_reporting_exchange_rates_history ex
         ON ex.from_currency = k.currency
        AND ex.to_currency = 'CAD'
        AND ex.snapshot_date::DATE = k.date::DATE
  LEFT JOIN dwh_ai.ai_reporting_exchange_rates_history ex2
         ON ex2.from_currency = 'USD'
        AND ex2.to_currency = 'CAD'
        AND ex2.snapshot_date::DATE = k.date::DATE
WHERE 1 = 1
AND   k.date >= '2025-01-01'
AND   k.sales_channel_type = 'Amazon'
GROUP BY k.date,
         k.brand,
         b.group,
         merchant,
         marketplace
)
SELECT brand,
       brand_group,
       merchant,
       marketplace,
       currency,
       SUM(adspend) / SUM(net_revenue) AS tacos
FROM
(
  SELECT brand, brand_group, merchant, marketplace, currency, net_revenue, 0 as adspend from tmp_net_revenue
  UNION ALL
  SELECT brand, brand_group, merchant, marketplace, currency, 0 net_revenue, adspend from tmp_adspend
) 
GROUP BY brand,
         brand_group,
         merchant,
         marketplace,
         currency
HAVING SUM(net_revenue) > 0
;",OK
Excess Units,"SELECT b.group AS brand_group,
       i.brand,
       i.merchant,
       i.marketplace,
       SUM(NVL (i.total_excess_units,0)) total_excess_units
FROM dwh_ai.ai_reporting_ds_amazon_inventory i
  LEFT JOIN dwh_ai.ai_reporting_brands b ON i.brand = b.name
WHERE 1 = 1
AND   i.snapshot_date = CURRENT_DATE
GROUP BY b.group,
         i.brand,
         i.merchant,
         i.marketplace;
","SELECT b.group AS brand_group,
       i.brand,
       i.merchant,
       i.marketplace,
       SUM(NVL (i.total_excess_units,0)) total_excess_units
FROM dwh_ai.ai_reporting_ds_amazon_inventory i
  LEFT JOIN dwh_ai.ai_reporting_brands b ON i.brand = b.name
WHERE 1 = 1
AND   i.snapshot_date = CURRENT_DATE
GROUP BY b.group,
         i.brand,
         i.merchant,
         i.marketplace;
",OK
Instock Rate,"SELECT b.group AS brand_group,
       i.brand,
       i.merchant,
       i.marketplace,
       i.product_segment_abc_class,
       SUM(NVL (i.total_instock_fba_skus,0))/SUM(NVL (i.total_fba_skus,0))::FLOAT8 instock_rate
FROM dwh_ai.ai_reporting_ds_amazon_inventory i
  LEFT JOIN dwh_ai.ai_reporting_brands b ON i.brand = b.name
WHERE 1 = 1
AND   i.snapshot_date = CURRENT_DATE
GROUP BY b.group,
         i.brand,
         i.merchant,
         i.marketplace,
         i.product_segment_abc_class;","SELECT b.group AS brand_group,
       i.brand,
       i.merchant,
       i.marketplace,
       i.product_segment_abc_class,
       SUM(NVL (i.total_instock_fba_skus,0))/SUM(NVL (i.total_fba_skus,0))::FLOAT8 instock_rate
FROM dwh_ai.ai_reporting_ds_amazon_inventory i
  LEFT JOIN dwh_ai.ai_reporting_brands b ON i.brand = b.name
WHERE 1 = 1
AND   i.snapshot_date = CURRENT_DATE
GROUP BY b.group,
         i.brand,
         i.merchant,
         i.marketplace,
         i.product_segment_abc_class;",OK