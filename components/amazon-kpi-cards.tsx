"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card";
import React, { useCallback, useEffect, useState } from 'react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "./ui/tooltip";
import { formatCurrency, formatNumber, formatPercent } from "@/lib/chart-utils";

import { Button } from "./ui/button";
import { HelpCircle } from "lucide-react";
import { Skeleton } from "./ui/skeleton";
import { useFilters } from '@/lib/contexts/filter-context';

type DisplayMode = "value" | "percent";

type AmazonKpiPair = {
  id: string;
  name: string;
  valueKey: string;
  percentKey: string | null;
  hasPercentage: boolean;
  definition: string;
  isPercentage?: boolean;
  isCurrency?: boolean;
};

// Amazon-specific KPI definitions
const AMAZON_KPI_PAIRS: AmazonKpiPair[] = [
  {
    id: "net_revenue",
    name: "Net Revenue",
    valueKey: "Net Revenue",
    percentKey: null,
    hasPercentage: false,
    definition: "Total net revenue from Amazon sales",
    isCurrency: true
  },
  {
    id: "branded_acos",
    name: "Branded ACOS",
    valueKey: "Branded ACOS",
    percentKey: null,
    hasPercentage: false,
    definition: "Advertising Cost of Sales for branded campaigns (campaigns containing 'BC')",
    isPercentage: true
  },
  {
    id: "unbranded_acos",
    name: "Unbranded ACOS",
    valueKey: "Unbranded ACOS",
    percentKey: null,
    hasPercentage: false,
    definition: "Advertising Cost of Sales for unbranded campaigns (campaigns not containing 'BC')",
    isPercentage: true
  },
  {
    id: "adspend",
    name: "Adspend",
    valueKey: "Adspend",
    percentKey: null,
    hasPercentage: false,
    definition: "Total advertising spend on Amazon",
    isCurrency: true
  },
  {
    id: "tacos",
    name: "TACOS",
    valueKey: "TACOS",
    percentKey: null,
    hasPercentage: false,
    definition: "Total Advertising Cost of Sales (Adspend / Net Revenue)",
    isPercentage: true
  },
  {
    id: "excess_units",
    name: "Excess Units",
    valueKey: "Excess Units",
    percentKey: null,
    hasPercentage: false,
    definition: "Total excess inventory units in Amazon warehouses"
  },
  {
    id: "instock_rate",
    name: "Instock Rate",
    valueKey: "Instock Rate",
    percentKey: null,
    hasPercentage: false,
    definition: "Percentage of SKUs that are in stock (Instock FBA SKUs / Total FBA SKUs)",
    isPercentage: true
  }
];

// Amazon KPI data type
type AmazonKpiData = {
  [key: string]: {
    summary: { value: number | null };
    timeSeries: { date: string; value: number | null }[];
  };
};

function AmazonKpiCardsContent() {
  const { filters, getQueryParams } = useFilters();
  const [data, setData] = useState<AmazonKpiData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [displayMode, setDisplayMode] = useState<DisplayMode>("value");

  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const queryParams = getQueryParams();
      const response = await fetch(`/api/amazon/kpis?${queryParams}`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      setData(result);
    } catch (err) {
      console.error('Error fetching Amazon KPI data:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch data');
    } finally {
      setLoading(false);
    }
  }, [getQueryParams]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const formatValue = (value: number | null, kpi: AmazonKpiPair): string => {
    if (value === null || value === undefined) return 'N/A';

    if (kpi.isPercentage) {
      return formatPercent(value);
    } else if (kpi.isCurrency) {
      return formatCurrency(value, filters.currency);
    } else {
      return formatNumber(value);
    }
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {AMAZON_KPI_PAIRS.map((kpi) => (
          <Card key={kpi.id}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                <Skeleton className="h-4 w-24" />
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-20 mb-2" />
              <Skeleton className="h-3 w-32" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        <Card className="col-span-full">
          <CardHeader>
            <CardTitle className="text-destructive">Error Loading Amazon KPIs</CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={fetchData} variant="outline">
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Display Mode Toggle */}
      <div className="flex justify-end">
        <div className="flex rounded-md border">
          <Button
            variant={displayMode === "value" ? "default" : "ghost"}
            size="sm"
            onClick={() => setDisplayMode("value")}
            className="rounded-r-none"
          >
            Values
          </Button>
          <Button
            variant={displayMode === "percent" ? "default" : "ghost"}
            size="sm"
            onClick={() => setDisplayMode("percent")}
            className="rounded-l-none"
          >
            Percentages
          </Button>
        </div>
      </div>

      {/* KPI Cards Grid */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {AMAZON_KPI_PAIRS.map((kpi) => {
          const kpiData = data?.[kpi.valueKey];
          const value = kpiData?.summary?.value;

          return (
            <Card key={kpi.id}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{kpi.name}</CardTitle>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <HelpCircle className="h-4 w-4 text-muted-foreground" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs">{kpi.definition}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatValue(value, kpi)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Amazon marketplace data
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}

export function AmazonKpiCards() {
  return <AmazonKpiCardsContent />;
}
